import { TRPCError } from "@trpc/server";
import { z } from "zod";
import {
	createTRPCRouter as router,
	publicProcedure,
	protectedProcedure,
} from "../trpc";
import { eq, count, and, desc } from "drizzle-orm";
import { users, cats, favorites, chatParticipants } from "@/lib/db/schema";
import { generateUniqueUserSlug } from "@/lib/utils/slug";
import { formatCatSummary } from "./helpers/cat-helpers";

// Input schema for updating user profile
const profileUpdateSchema = z.object({
	name: z.string().optional(),
	bio: z.string().optional(),
	location: z.string().optional(), // Keep for backward compatibility
	wilayaId: z.number().optional(),
	communeId: z.number().optional(),
	phone: z.string().optional(),
	image: z.string().optional(),
	slug: z.string().optional(),
});

export const usersRouter = router({
	// Get the current user's profile
	getProfile: protectedProcedure.query(async ({ ctx }) => {
		const { user } = ctx;

		// Convert the string ID from auth to a number for the database query
		const userId = Number(user.id);

		if (isNaN(userId)) {
			throw new TRPCError({
				code: "BAD_REQUEST",
				message: "Invalid user ID",
			});
		}

		const userProfile = await ctx.db.query.users.findFirst({
			where: eq(users.id, userId),
			with: {
				wilaya: true,
				commune: true,
			},
		});

		if (!userProfile) {
			throw new TRPCError({
				code: "NOT_FOUND",
				message: "User profile not found",
			});
		}

		return userProfile;
	}),

	// Get current user's statistics for profile sidebar
	getUserStats: protectedProcedure.query(async ({ ctx }) => {
		const { user } = ctx;

		// Convert the string ID from auth to a number for the database query
		const userId = Number(user.id);

		if (isNaN(userId)) {
			throw new TRPCError({
				code: "BAD_REQUEST",
				message: "Invalid user ID",
			});
		}

		// Get user's cats count (excluding drafts)
		const [catsCountResult] = await ctx.db
			.select({ value: count() })
			.from(cats)
			.where(and(eq(cats.userId, userId), eq(cats.isDraft, false)));

		// Get user's favorites count
		const [favoritesCountResult] = await ctx.db
			.select({ value: count() })
			.from(favorites)
			.where(eq(favorites.userId, userId));

		// Get user's messages/chats count
		const [messagesCountResult] = await ctx.db
			.select({ value: count() })
			.from(chatParticipants)
			.where(eq(chatParticipants.userId, userId));

		return {
			listedCats: catsCountResult.value,
			favorites: favoritesCountResult.value,
			messages: messagesCountResult.value,
		};
	}),

	// Update the current user's profile
	updateProfile: protectedProcedure
		.input(profileUpdateSchema)
		.mutation(async ({ ctx, input }) => {
			const { user } = ctx;

			// Convert the string ID from auth to a number for the database query
			const userId = Number(user.id);

			if (isNaN(userId)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid user ID",
				});
			}

			// Get current user data to check if name changed
			const currentUser = await ctx.db.query.users.findFirst({
				where: eq(users.id, userId),
			});

			if (!currentUser) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "User not found",
				});
			}

			// Generate new slug if name is being updated
			let updateData = { ...input, updatedAt: new Date() };
			if (input.name && input.name !== currentUser.name) {
				const newSlug = await generateUniqueUserSlug(
					input.name,
					userId
				);
				updateData.slug = newSlug;
			}

			// Update user profile
			const [updatedUser] = await ctx.db
				.update(users)
				.set(updateData)
				.where(eq(users.id, userId))
				.returning();

			if (!updatedUser) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to update profile",
				});
			}

			return updatedUser;
		}),

	// Get user by ID (public, but returns limited info)
	getUserById: publicProcedure
		.input(z.object({ userId: z.number() }))
		.query(async ({ ctx, input }) => {
			const user = await ctx.db.query.users.findFirst({
				where: eq(users.id, input.userId),
			});

			if (!user) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "User not found",
				});
			}

			// Return limited public information
			return {
				id: user.id,
				name: user.name,
				role: user.role,
				bio: user.bio,
				location: user.location,
				image: user.image,
			};
		}),

	// Get user by slug (public, but returns limited info)
	getBySlug: publicProcedure
		.input(z.string())
		.query(async ({ ctx, input }) => {
			const user = await ctx.db.query.users.findFirst({
				where: eq(users.slug, input),
			});

			if (!user) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "User not found",
				});
			}

			// Return limited public information
			return {
				id: user.id,
				name: user.name,
				slug: user.slug,
				role: user.role,
				bio: user.bio,
				location: user.location,
				image: user.image,
			};
		}),

	// Delete account (for users who want to remove their account)
	deleteAccount: protectedProcedure.mutation(async ({ ctx }) => {
		const { user } = ctx;

		// Convert the string ID from auth to a number for the database query
		const userId = Number(user.id);

		if (isNaN(userId)) {
			throw new TRPCError({
				code: "BAD_REQUEST",
				message: "Invalid user ID",
			});
		}

		try {
			await ctx.db.delete(users).where(eq(users.id, userId));
			return { success: true };
		} catch (error) {
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to delete account",
			});
		}
	}),

	// Get public user profile with statistics
	getPublicProfile: publicProcedure
		.input(z.string())
		.query(async ({ ctx, input }) => {
			const user = await ctx.db.query.users.findFirst({
				where: eq(users.slug, input),
			});

			if (!user) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "User not found",
				});
			}

			// Get user statistics
			const [totalCatsResult] = await ctx.db
				.select({ value: count() })
				.from(cats)
				.where(and(eq(cats.userId, user.id), eq(cats.isDraft, false)));

			const [adoptedCatsResult] = await ctx.db
				.select({ value: count() })
				.from(cats)
				.where(
					and(
						eq(cats.userId, user.id),
						eq(cats.adopted, true),
						eq(cats.isDraft, false)
					)
				);

			// Return public profile with statistics
			return {
				id: user.id,
				name: user.name,
				slug: user.slug,
				role: user.role,
				bio: user.bio,
				location: user.location,
				image: user.image,
				createdAt: user.createdAt,
				stats: {
					catsListed: totalCatsResult.value,
					adoptions: adoptedCatsResult.value,
					// TODO: Add reviews count when reviews system is implemented
					reviews: 0,
					rating: 5.0, // Mock rating for now
				},
			};
		}),

	// Get public user cats (for profile page)
	getPublicUserCats: publicProcedure
		.input(
			z.object({
				userSlug: z.string(),
				adopted: z.boolean().optional(),
				page: z.number().default(1),
				limit: z.number().default(12),
			})
		)
		.query(async ({ ctx, input }) => {
			// First get the user
			const user = await ctx.db.query.users.findFirst({
				where: eq(users.slug, input.userSlug),
			});

			if (!user) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "User not found",
				});
			}

			// Build query conditions
			let conditions = [
				eq(cats.userId, user.id),
				eq(cats.isDraft, false), // Only show published cats
			];

			// Filter by adoption status if specified
			if (input.adopted !== undefined) {
				conditions.push(eq(cats.adopted, input.adopted));
			}

			// Calculate pagination
			const offset = (input.page - 1) * input.limit;

			// Get cats with related data
			const userCats = await ctx.db.query.cats.findMany({
				where: and(...conditions),
				with: {
					images: true,
					breed: true,
					wilaya: true,
					commune: true,
				},
				orderBy: desc(cats.createdAt),
				limit: input.limit,
				offset,
			});

			// Get total count for pagination
			const [totalResult] = await ctx.db
				.select({ value: count() })
				.from(cats)
				.where(and(...conditions));

			// Format cats using the existing formatter
			const formattedCats = userCats.map(formatCatSummary);

			return {
				cats: formattedCats,
				pagination: {
					page: input.page,
					limit: input.limit,
					total: totalResult.value,
					totalPages: Math.ceil(totalResult.value / input.limit),
				},
			};
		}),
});
